/**
 * Monitoring Middleware for Skill Gap Analyzer
 * Automatic performance tracking and error monitoring for API routes
 */

import { NextRequest, NextResponse } from 'next/server';
import { skillGapMetrics } from './metrics';
import { skillGapLogger } from './logger';
import { skillGapAlerting } from './alerting';

export interface MonitoringConfig {
  trackPerformance: boolean;
  trackErrors: boolean;
  trackUserBehavior: boolean;
  alertOnSlowRequests: boolean;
  slowRequestThreshold: number; // milliseconds
  alertOnErrors: boolean;
  excludePaths?: string[];
}

const defaultConfig: MonitoringConfig = {
  trackPerformance: true,
  trackErrors: true,
  trackUserBehavior: true,
  alertOnSlowRequests: true,
  slowRequestThreshold: 5000,
  alertOnErrors: true,
  excludePaths: ['/api/monitoring', '/api/health'],
};

/**
 * Monitoring middleware for API routes
 */
export function withMonitoring(
  handler: (req: NextRequest) => Promise<NextResponse>,
  config: Partial<MonitoringConfig> = {}
) {
  const finalConfig = { ...defaultConfig, ...config };

  return async (req: NextRequest): Promise<NextResponse> => {
    const startTime = Date.now();
    const requestId = generateRequestId();
    const pathname = new URL(req.url).pathname;
    const method = req.method;

    // Skip monitoring for excluded paths
    if (finalConfig.excludePaths?.some(path => pathname.startsWith(path))) {
      return handler(req);
    }

    // Extract user context
    const userId = extractUserId(req);
    const sessionId = extractSessionId(req);

    let response: NextResponse;
    let error: Error | null = null;

    try {
      // Add monitoring headers to request
      const monitoredRequest = addMonitoringHeaders(req, requestId);
      
      // Execute the handler
      response = await handler(monitoredRequest);
      
    } catch (err) {
      error = err as Error;
      
      // Create error response
      response = NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    const endTime = Date.now();
    const duration = endTime - startTime;
    const statusCode = response.status;

    // Track performance metrics
    if (finalConfig.trackPerformance) {
      trackPerformanceMetrics({
        endpoint: pathname,
        method,
        duration,
        statusCode,
        userId,
        sessionId,
        requestId,
      });
    }

    // Track errors
    if (error && finalConfig.trackErrors) {
      trackErrorMetrics({
        error,
        endpoint: pathname,
        method,
        statusCode,
        userId,
        sessionId,
        requestId,
      });
    }

    // Track user behavior
    if (finalConfig.trackUserBehavior && userId) {
      trackUserBehaviorMetrics({
        endpoint: pathname,
        method,
        duration,
        statusCode,
        userId,
        sessionId,
      });
    }

    // Check for alerts
    if (finalConfig.alertOnSlowRequests && duration > finalConfig.slowRequestThreshold) {
      await skillGapAlerting.createAlert({
        title: 'Slow API Request',
        message: `${method} ${pathname} took ${duration}ms (threshold: ${finalConfig.slowRequestThreshold}ms)`,
        severity: duration > finalConfig.slowRequestThreshold * 2 ? 'high' : 'medium',
        source: 'performance_monitoring',
        metadata: {
          endpoint: pathname,
          method,
          duration,
          userId,
          requestId,
        },
      });
    }

    if (finalConfig.alertOnErrors && error) {
      await skillGapAlerting.createAlert({
        title: 'API Error',
        message: `${method} ${pathname} failed: ${error.message}`,
        severity: statusCode >= 500 ? 'high' : 'medium',
        source: 'error_monitoring',
        metadata: {
          endpoint: pathname,
          method,
          statusCode,
          error: error.message,
          userId,
          requestId,
        },
      });
    }

    // Add monitoring headers to response
    response.headers.set('X-Request-ID', requestId);
    response.headers.set('X-Response-Time', duration.toString());

    return response;
  };
}

/**
 * Skill Gap specific monitoring wrapper
 */
export function withSkillGapMonitoring(
  handler: (req: NextRequest) => Promise<NextResponse>
) {
  return withMonitoring(handler, {
    trackPerformance: true,
    trackErrors: true,
    trackUserBehavior: true,
    alertOnSlowRequests: true,
    slowRequestThreshold: 3000, // 3 seconds for skill gap operations
    alertOnErrors: true,
  });
}

/**
 * Performance tracking for specific skill gap operations
 */
export function trackSkillGapOperation(
  operation: string,
  duration: number,
  metadata: Record<string, any> = {}
) {
  skillGapMetrics.recordMetric({
    name: `skill_gap_${operation}_duration`,
    value: duration,
    unit: 'ms',
    metadata,
  });

  skillGapLogger.logPerformance({
    endpoint: `/skill-gap/${operation}`,
    method: 'INTERNAL',
    statusCode: 200,
    duration,
    metadata,
  });
}

/**
 * Track skill gap specific user actions
 */
export function trackSkillGapUserAction(
  action: string,
  userId: string,
  properties: Record<string, any> = {}
) {
  skillGapMetrics.recordUserBehavior(action, userId, properties);
  
  skillGapLogger.logUserBehavior({
    event: action,
    userId,
    properties,
  });
}

/**
 * Helper functions
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function extractUserId(req: NextRequest): string | undefined {
  // Extract user ID from various sources
  const authHeader = req.headers.get('authorization');
  const userIdHeader = req.headers.get('x-user-id');
  const sessionCookie = req.cookies.get('session')?.value;

  // Try to extract from JWT token
  if (authHeader?.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7);
      // In a real implementation, you would decode the JWT
      // For now, return a placeholder
      return `user_from_jwt_${token.substring(0, 8)}`;
    } catch {
      // Invalid token
    }
  }

  // Try user ID header
  if (userIdHeader) {
    return userIdHeader;
  }

  // Try session cookie
  if (sessionCookie) {
    return `user_from_session_${sessionCookie.substring(0, 8)}`;
  }

  return undefined;
}

function extractSessionId(req: NextRequest): string | undefined {
  const sessionCookie = req.cookies.get('session')?.value;
  const sessionHeader = req.headers.get('x-session-id');
  
  return sessionHeader || sessionCookie || undefined;
}

function addMonitoringHeaders(req: NextRequest, requestId: string): NextRequest {
  // Clone the request and add monitoring headers
  const headers = new Headers(req.headers);
  headers.set('x-request-id', requestId);
  headers.set('x-monitoring-enabled', 'true');
  
  return new NextRequest(req.url, {
    method: req.method,
    headers,
    body: req.body,
  });
}

function trackPerformanceMetrics(data: {
  endpoint: string;
  method: string;
  duration: number;
  statusCode: number;
  userId?: string;
  sessionId?: string;
  requestId: string;
}) {
  // Record general performance metrics
  skillGapMetrics.recordMetric({
    name: 'api_response_time',
    value: data.duration,
    unit: 'ms',
    tags: {
      endpoint: data.endpoint,
      method: data.method,
      status_code: data.statusCode.toString(),
    },
  });

  // Record endpoint-specific metrics
  const endpointName = data.endpoint.replace(/^\/api\//, '').replace(/\//g, '_');
  skillGapMetrics.recordMetric({
    name: `${endpointName}_response_time`,
    value: data.duration,
    unit: 'ms',
  });

  // Log performance
  skillGapLogger.logPerformance({
    endpoint: data.endpoint,
    method: data.method,
    statusCode: data.statusCode,
    duration: data.duration,
    userId: data.userId,
    sessionId: data.sessionId,
    requestId: data.requestId,
  });
}

function trackErrorMetrics(data: {
  error: Error;
  endpoint: string;
  method: string;
  statusCode: number;
  userId?: string;
  sessionId?: string;
  requestId: string;
}) {
  // Record error metrics
  skillGapMetrics.recordError(
    data.error.name,
    data.endpoint,
    data.statusCode
  );

  // Log error
  skillGapLogger.logError(data.error, {
    feature: 'api_monitoring',
    action: 'api_request',
    userId: data.userId,
    sessionId: data.sessionId,
    requestId: data.requestId,
    metadata: {
      endpoint: data.endpoint,
      method: data.method,
      statusCode: data.statusCode,
    },
  });
}

function trackUserBehaviorMetrics(data: {
  endpoint: string;
  method: string;
  duration: number;
  statusCode: number;
  userId: string;
  sessionId?: string;
}) {
  const action = `api_${data.method.toLowerCase()}_${data.endpoint.replace(/^\/api\//, '').replace(/\//g, '_')}`;
  
  skillGapMetrics.recordUserBehavior(action, data.userId, {
    endpoint: data.endpoint,
    method: data.method,
    duration: data.duration,
    statusCode: data.statusCode,
    sessionId: data.sessionId,
  });
}

/**
 * Monitoring decorator for class methods
 */
export function MonitorMethod(operationName?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    const operation = operationName || `${target.constructor.name}.${propertyKey}`;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await originalMethod.apply(this, args);
        const duration = Date.now() - startTime;
        
        trackSkillGapOperation(operation, duration, {
          success: true,
          args: args.length,
        });
        
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        
        trackSkillGapOperation(operation, duration, {
          success: false,
          error: (error as Error).message,
          args: args.length,
        });
        
        throw error;
      }
    };

    return descriptor;
  };
}
