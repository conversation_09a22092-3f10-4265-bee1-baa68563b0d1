const mockFormData = { email: '', password: '' };

const mockUpdateField = jest.fn((field: string, value: string) => {
  (mockFormData as any)[field] = value;
});

const mockHandleSubmit = jest.fn((e?: any) => {
  if (e && e.preventDefault) {
    e.preventDefault();
  }
  // Simulate form submission logic
  return Promise.resolve();
});

export const useValidatedForm = jest.fn(() => ({
  data: mockFormData,
  updateField: mockUpdateField,
  handleSubmit: mockHandleSubmit,
  isSubmitting: false,
  validation: {
    errors: {},
    isValid: true,
  },
  validationActions: {
    validateField: jest.fn(),
    validateForm: jest.fn(),
    clearErrors: jest.fn(),
  },
}));
