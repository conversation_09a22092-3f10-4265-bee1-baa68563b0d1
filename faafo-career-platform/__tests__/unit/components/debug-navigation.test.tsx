/**
 * Debug Navigation Test - Minimal test to isolate issues
 */

import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';

// Very simple test to check if the test environment is working
describe('Debug Navigation Test', () => {
  it('should render a simple div without any mocks', () => {
    render(<div data-testid="debug-test">Debug Test</div>);
    expect(screen.getByTestId('debug-test')).toBeInTheDocument();
    expect(screen.getByText('Debug Test')).toBeInTheDocument();
  });

  it('should verify basic Jest functionality', () => {
    const mockFn = jest.fn();
    mockFn('test');
    expect(mockFn).toHaveBeenCalledWith('test');
    expect(mockFn).toHaveBeenCalledTimes(1);
  });
});
