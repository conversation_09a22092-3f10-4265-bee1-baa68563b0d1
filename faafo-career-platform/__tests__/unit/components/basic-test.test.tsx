/**
 * Basic Test Infrastructure Validation
 * 
 * Tests basic React Testing Library functionality without complex components
 * to isolate and fix React Act warnings and DOM cleanup issues.
 * 
 * @category unit
 * @requires React Testing Library
 */

import React from 'react';
import { render, screen, cleanup, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';

// Use the window.matchMedia mock from jest.setup.js

// Simple component for testing
function SimpleComponent() {
  return <div data-testid="simple-component">Hello Test</div>;
}

// Component with state to test React Act warnings
function StatefulComponent() {
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    // This should not cause React Act warnings if properly handled
    setCount(1);
  }, []);

  return <div data-testid="stateful-component">Count: {count}</div>;
}

describe('Basic Test Infrastructure', () => {
  afterEach(() => {
    cleanup();
  });

  it('should render a simple component without errors', () => {
    render(<SimpleComponent />);
    expect(screen.getByTestId('simple-component')).toBeInTheDocument();
    expect(screen.getByText('Hello Test')).toBeInTheDocument();
  });

  it('should handle stateful components without React Act warnings', async () => {
    const { getByTestId, getByText } = render(<StatefulComponent />);

    // Component should render immediately
    expect(getByTestId('stateful-component')).toBeInTheDocument();

    // Wait for the state update to complete
    await waitFor(() => {
      expect(getByText('Count: 1')).toBeInTheDocument();
    });
  });

  it('should verify window.matchMedia is properly mocked', () => {
    expect(window.matchMedia).toBeDefined();

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    expect(mediaQuery).toBeDefined();
    expect(mediaQuery.matches).toBe(false);
    expect(mediaQuery.media).toBe('(prefers-color-scheme: dark)');
    expect(mediaQuery.addEventListener).toBeDefined();
    expect(mediaQuery.removeEventListener).toBeDefined();
  });

  it('should verify localStorage is properly mocked', () => {
    expect(localStorage).toBeDefined();
    localStorage.setItem('test', 'value');
    expect(localStorage.getItem('test')).toBe('value');
    localStorage.removeItem('test');
    expect(localStorage.getItem('test')).toBeNull();
  });
});
