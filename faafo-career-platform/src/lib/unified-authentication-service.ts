/**
 * Unified Authentication Service
 * 
 * Consolidates all authentication and session validation logic into a single,
 * consistent service to eliminate architectural inconsistencies and provide
 * unified authentication handling across the application.
 * 
 * This service replaces:
 * - SessionSecurity (session-security.ts)
 * - UnifiedSessionManagement (unified-session-management.ts) 
 * - UserValidationService (user-validation-service.ts)
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import securityStorage from '@/lib/security-storage';

// Unified interfaces
export interface UnifiedSessionValidationResult {
  isValid: boolean;
  userId?: string;
  user?: {
    id: string;
    email: string;
    name: string | null;
    emailVerified: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
  };
  session?: any;
  sessionData?: any;
  error?: string;
  statusCode?: number;
  securityFlags?: string[];
}

export interface SessionValidationOptions {
  requireEmailVerification?: boolean;
  checkAccountLock?: boolean;
  validateUserExists?: boolean;
  refreshSession?: boolean;
  sessionType?: 'interview' | 'learning' | 'assessment' | 'general';
  requiredPermissions?: string[];
  enableSecurityLogging?: boolean;
  enableAntiEnumeration?: boolean;
}

export interface SessionCreationData {
  sessionType: string;
  careerPath?: string;
  experienceLevel?: string;
  companyType?: string;
  industryFocus?: string;
  specificRole?: string;
  interviewType?: string;
  preparationTime?: string;
  focusAreas?: string[];
  difficulty: string;
  totalQuestions: number;
}

export interface SessionUpdateData {
  status?: 'NOT_STARTED' | 'IN_PROGRESS' | 'PAUSED' | 'COMPLETED' | 'ABANDONED';
  completedQuestions?: number;
  timeSpent?: number;
  currentQuestionIndex?: number;
  lastActivityAt?: Date;
}

export class UnifiedAuthenticationService {
  private static readonly MAX_SESSION_AGE = 24 * 60 * 60 * 1000; // 24 hours
  private static readonly SESSION_ID_PATTERN = /^[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}$/i;

  /**
   * Primary session validation method - replaces all other validation methods
   */
  static async validateSession(
    request: NextRequest,
    options: SessionValidationOptions = {}
  ): Promise<UnifiedSessionValidationResult> {
    const {
      requireEmailVerification = false,
      checkAccountLock = true,
      validateUserExists = true,
      refreshSession = false,
      sessionType = 'general',
      requiredPermissions = [],
      enableSecurityLogging = true,
      enableAntiEnumeration = true
    } = options;

    try {
      // 1. Get session from NextAuth
      const session = await getServerSession(authOptions);
      
      if (!session?.user?.id) {
        return {
          isValid: false,
          error: 'Authentication required',
          statusCode: 401
        };
      }

      const userId = session.user.id;

      // 2. Validate user exists in database (if required)
      let user = null;
      if (validateUserExists) {
        user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            email: true,
            name: true,
            emailVerified: true,
            createdAt: true,
            updatedAt: true,
            lockedUntil: true,
            failedLoginAttempts: true
          }
        });

        if (!user) {
          if (enableSecurityLogging) {
            await this.logSecurityEvent(request, userId, 'user_not_found', {
              sessionType,
              timestamp: new Date().toISOString()
            });
          }

          return {
            isValid: false,
            error: enableAntiEnumeration ? 'Authentication required' : 'User not found',
            statusCode: 401,
            securityFlags: ['USER_NOT_FOUND']
          };
        }

        // 3. Check account lock status
        if (checkAccountLock && user.lockedUntil && user.lockedUntil > new Date()) {
          if (enableSecurityLogging) {
            await this.logSecurityEvent(request, userId, 'account_locked_access_attempt', {
              lockedUntil: user.lockedUntil,
              sessionType
            });
          }

          return {
            isValid: false,
            error: 'Account is temporarily locked',
            statusCode: 423,
            securityFlags: ['ACCOUNT_LOCKED']
          };
        }

        // 4. Check email verification (if required)
        if (requireEmailVerification && !user.emailVerified) {
          return {
            isValid: false,
            error: 'Email verification required',
            statusCode: 403,
            securityFlags: ['EMAIL_NOT_VERIFIED']
          };
        }
      }

      // 5. Ensure user relationships exist (prevent foreign key errors)
      if (validateUserExists) {
        await this.ensureUserRelationships(userId);
      }

      // 6. Session refresh (if requested)
      if (refreshSession) {
        await this.updateLastActivity(userId);
      }

      // 7. Permission validation (if required)
      if (requiredPermissions.length > 0) {
        const hasPermissions = await this.validatePermissions(userId, requiredPermissions);
        if (!hasPermissions) {
          return {
            isValid: false,
            error: 'Insufficient permissions',
            statusCode: 403,
            securityFlags: ['INSUFFICIENT_PERMISSIONS']
          };
        }
      }

      // Success - return unified result
      return {
        isValid: true,
        userId,
        user: user ? {
          id: user.id,
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        } : undefined,
        session,
        statusCode: 200
      };

    } catch (error) {
      console.error('Unified session validation error:', error);
      
      if (enableSecurityLogging) {
        await this.logSecurityEvent(request, 'unknown', 'validation_error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          sessionType
        });
      }

      return {
        isValid: false,
        error: 'Session validation failed',
        statusCode: 500,
        securityFlags: ['VALIDATION_ERROR']
      };
    }
  }

  /**
   * Validate session access for specific session ID (replaces SessionSecurity.validateSessionAccess)
   */
  static async validateSessionAccess(
    request: NextRequest,
    sessionId: string,
    options: SessionValidationOptions = {}
  ): Promise<UnifiedSessionValidationResult> {
    const {
      sessionType = 'interview',
      enableAntiEnumeration = true,
      enableSecurityLogging = true
    } = options;

    try {
      // 1. Basic session ID format validation
      if (!this.isValidSessionIdFormat(sessionId)) {
        return {
          isValid: false,
          error: enableAntiEnumeration ? 'Session not found' : 'Invalid session ID format',
          statusCode: 404,
          securityFlags: ['INVALID_SESSION_FORMAT']
        };
      }

      // 2. Validate user session first
      const userValidation = await this.validateSession(request, options);
      if (!userValidation.isValid) {
        return userValidation;
      }

      const userId = userValidation.userId!;

      // 3. Validate session ownership and existence
      const sessionData = await this.validateSessionOwnership(sessionId, userId, sessionType);
      if (!sessionData) {
        if (enableSecurityLogging) {
          await this.logSecurityEvent(request, userId, 'session_enumeration_attempt', {
            sessionId,
            sessionType
          });
        }

        return {
          isValid: false,
          error: 'Session not found',
          statusCode: 404,
          securityFlags: ['SESSION_NOT_FOUND']
        };
      }

      // 4. Validate session age and status
      const sessionValidation = await this.validateSessionStatus(sessionData, sessionType);
      if (!sessionValidation.isValid) {
        return {
          isValid: false,
          error: sessionValidation.error,
          statusCode: sessionValidation.statusCode || 400,
          securityFlags: ['SESSION_INVALID_STATUS']
        };
      }

      // Success
      return {
        isValid: true,
        userId,
        user: userValidation.user,
        session: userValidation.session,
        sessionData,
        statusCode: 200
      };

    } catch (error) {
      console.error('Session access validation error:', error);
      return {
        isValid: false,
        error: 'Session validation failed',
        statusCode: 500,
        securityFlags: ['ACCESS_VALIDATION_ERROR']
      };
    }
  }

  /**
   * Private helper methods
   */
  private static isValidSessionIdFormat(sessionId: string): boolean {
    return this.SESSION_ID_PATTERN.test(sessionId);
  }

  private static async validateSessionOwnership(
    sessionId: string,
    userId: string,
    sessionType: string
  ): Promise<any> {
    switch (sessionType) {
      case 'interview':
        return await prisma.interviewSession.findFirst({
          where: { id: sessionId, userId }
        });
      case 'learning':
        // Use userLearningPath for learning sessions
        return await prisma.userLearningPath.findFirst({
          where: { id: sessionId, userId }
        });
      case 'assessment':
        return await prisma.assessment.findFirst({
          where: { id: sessionId, userId }
        });
      default:
        return null;
    }
  }

  private static async validateSessionStatus(sessionData: any, sessionType: string): Promise<{
    isValid: boolean;
    error?: string;
    statusCode?: number;
  }> {
    // Check session age
    if (sessionData.createdAt) {
      const age = Date.now() - new Date(sessionData.createdAt).getTime();
      if (age > this.MAX_SESSION_AGE) {
        return {
          isValid: false,
          error: 'Session expired',
          statusCode: 410
        };
      }
    }

    // Check session status
    if (sessionData.status === 'ABANDONED') {
      return {
        isValid: false,
        error: 'Session has been abandoned',
        statusCode: 410
      };
    }

    return { isValid: true };
  }

  private static async ensureUserRelationships(userId: string): Promise<void> {
    try {
      // Ensure Profile exists
      await prisma.profile.upsert({
        where: { userId },
        update: {},
        create: {
          userId,
          bio: '',
          currentCareerPath: '',
          progressLevel: '',
          currentIndustry: '',
          location: '',
          jobTitle: '',
          company: ''
        }
      });

      // Note: User relationships are created as needed by the application
      // No additional setup required for basic user validation
    } catch (error) {
      console.error('Error ensuring user relationships:', error);
      // Don't throw - this is a best-effort operation
    }
  }

  private static async updateLastActivity(userId: string): Promise<void> {
    try {
      // Update user's last activity timestamp
      await prisma.user.update({
        where: { id: userId },
        data: { updatedAt: new Date() }
      });
    } catch (error) {
      console.error('Failed to update last activity:', error);
    }
  }

  private static async validatePermissions(userId: string, permissions: string[]): Promise<boolean> {
    // Basic permission validation - can be extended
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { id: true }
    });

    if (!user) return false;

    // For now, all authenticated users have basic permissions
    // This can be extended with role-based permissions later
    const userPermissions = ['user', 'interview_practice', 'career_paths', 'resume_builder', 'progress_tracking'];

    return permissions.every(permission => userPermissions.includes(permission));
  }

  private static async logSecurityEvent(
    request: NextRequest,
    userId: string,
    eventType: string,
    details: Record<string, any>
  ): Promise<void> {
    try {
      // Check if we're in a static generation context
      // During static generation, request.headers is not available
      let ipAddress = 'unknown';
      let userAgent = 'unknown';

      try {
        // Only access headers if we're in a runtime context
        if (request && request.headers) {
          ipAddress = request.headers.get('x-forwarded-for') ||
                     request.headers.get('x-real-ip') ||
                     'unknown';
          userAgent = request.headers.get('user-agent') || 'unknown';
        }
      } catch (headerError) {
        // Headers not available (likely static generation)
        ipAddress = 'static-generation';
        userAgent = 'static-generation';
      }

      // Log security event (simplified for now)
      console.log('Security Event:', {
        userId,
        eventType,
        ipAddress,
        userAgent,
        details,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Failed to log security event:', error);
    }
  }

  /**
   * Session Management Methods (from UnifiedSessionManagement)
   */

  /**
   * Create a new session with proper validation
   */
  static async createSession(
    userId: string,
    sessionData: SessionCreationData
  ): Promise<UnifiedSessionValidationResult> {
    try {
      // Check for existing active sessions
      const existingActiveSession = await prisma.interviewSession.findFirst({
        where: {
          userId,
          status: {
            in: ['IN_PROGRESS', 'PAUSED']
          }
        }
      });

      if (existingActiveSession) {
        return {
          isValid: false,
          error: 'You already have an active session. Please complete or abandon it first.',
          statusCode: 409,
          securityFlags: ['ACTIVE_SESSION_EXISTS']
        };
      }

      // Create new session
      const newSession = await prisma.interviewSession.create({
        data: {
          userId,
          sessionType: sessionData.sessionType as any,
          careerPath: sessionData.careerPath,
          experienceLevel: sessionData.experienceLevel as any,
          companyType: sessionData.companyType as any,
          industryFocus: sessionData.industryFocus,
          specificRole: sessionData.specificRole,
          interviewType: sessionData.interviewType as any,
          preparationTime: sessionData.preparationTime,
          focusAreas: sessionData.focusAreas || [],
          difficulty: sessionData.difficulty as any,
          totalQuestions: sessionData.totalQuestions,
          status: 'NOT_STARTED',
          createdAt: new Date(),
          lastActiveAt: new Date()
        }
      });

      return {
        isValid: true,
        userId,
        sessionData: newSession,
        statusCode: 201
      };

    } catch (error) {
      console.error('Session creation error:', error);
      return {
        isValid: false,
        error: 'Failed to create session',
        statusCode: 500,
        securityFlags: ['SESSION_CREATION_ERROR']
      };
    }
  }

  /**
   * Update session with validation
   */
  static async updateSession(
    sessionId: string,
    userId: string,
    updateData: SessionUpdateData
  ): Promise<UnifiedSessionValidationResult> {
    try {
      // Validate session ownership
      const session = await prisma.interviewSession.findFirst({
        where: { id: sessionId, userId }
      });

      if (!session) {
        return {
          isValid: false,
          error: 'Session not found',
          statusCode: 404,
          securityFlags: ['SESSION_NOT_FOUND']
        };
      }

      // Validate status transition if status is being updated
      if (updateData.status) {
        const transitionValidation = this.validateSessionStateTransition(
          session.status,
          updateData.status,
          'interview'
        );

        if (!transitionValidation.isValid) {
          return {
            isValid: false,
            error: transitionValidation.error,
            statusCode: 400,
            securityFlags: ['INVALID_STATUS_TRANSITION']
          };
        }
      }

      // Update session
      const updatedSession = await prisma.interviewSession.update({
        where: { id: sessionId },
        data: {
          ...updateData,
          lastActiveAt: new Date()
        }
      });

      return {
        isValid: true,
        userId,
        sessionData: updatedSession,
        statusCode: 200
      };

    } catch (error) {
      console.error('Session update error:', error);
      return {
        isValid: false,
        error: 'Failed to update session',
        statusCode: 500,
        securityFlags: ['SESSION_UPDATE_ERROR']
      };
    }
  }

  /**
   * Validate session state transitions (from SessionSecurity)
   */
  static validateSessionStateTransition(
    currentStatus: string,
    newStatus: string,
    sessionType: string = 'interview'
  ): { isValid: boolean; error?: string } {
    const validTransitions: Record<string, Record<string, string[]>> = {
      interview: {
        'NOT_STARTED': ['IN_PROGRESS'],
        'IN_PROGRESS': ['PAUSED', 'COMPLETED', 'ABANDONED'],
        'PAUSED': ['IN_PROGRESS', 'ABANDONED'],
        'COMPLETED': [], // No transitions from completed
        'ABANDONED': [] // No transitions from abandoned
      },
      learning: {
        'NOT_STARTED': ['IN_PROGRESS'],
        'IN_PROGRESS': ['PAUSED', 'COMPLETED'],
        'PAUSED': ['IN_PROGRESS'],
        'COMPLETED': [], // Allow re-starting completed learning paths
        'ARCHIVED': []
      },
      assessment: {
        'NOT_STARTED': ['IN_PROGRESS'],
        'IN_PROGRESS': ['COMPLETED', 'ABANDONED'],
        'COMPLETED': [],
        'ABANDONED': []
      }
    };

    const allowedTransitions = validTransitions[sessionType]?.[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      return {
        isValid: false,
        error: `Invalid status transition from ${currentStatus} to ${newStatus}`
      };
    }

    return { isValid: true };
  }

  /**
   * Get user sessions with filtering
   */
  static async getUserSessions(
    userId: string,
    options: {
      sessionType?: string;
      status?: string;
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<UnifiedSessionValidationResult> {
    try {
      const { sessionType, status, limit = 10, offset = 0 } = options;

      const where: any = { userId };
      if (sessionType) where.sessionType = sessionType;
      if (status) where.status = status;

      const sessions = await prisma.interviewSession.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      });

      return {
        isValid: true,
        userId,
        sessionData: sessions,
        statusCode: 200
      };

    } catch (error) {
      console.error('Get user sessions error:', error);
      return {
        isValid: false,
        error: 'Failed to retrieve sessions',
        statusCode: 500,
        securityFlags: ['SESSION_RETRIEVAL_ERROR']
      };
    }
  }
}

export default UnifiedAuthenticationService;
