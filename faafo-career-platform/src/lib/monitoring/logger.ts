/**
 * Monitoring Logger Module
 * Stub implementation for build compatibility
 */

export const skillGapLogger = {
  log: (level: string, message: string, data?: any) => {
    console.log(`[${level.toUpperCase()}] ${message}`, data || '');
  },

  info: (message: string, data?: any) => {
    console.log(`[INFO] ${message}`, data || '');
  },

  warn: (message: string, data?: any) => {
    console.warn(`[WARN] ${message}`, data || '');
  },

  error: (message: string, data?: any) => {
    console.error(`[ERROR] ${message}`, data || '');
  },

  debug: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[DEBUG] ${message}`, data || '');
    }
  },

  getPerformanceData: () => ({
    timestamp: Date.now(),
    memory: process.memoryUsage(),
    uptime: process.uptime(),
    cpu: { usage: 0 }
  }),

  logUserBehavior: (data: any) => {
    console.log('[USER_BEHAVIOR]', data);
  }
};

export default skillGapLogger;
