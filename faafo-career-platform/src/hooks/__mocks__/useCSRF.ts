// Manual mock for useCSRF hook
const mockGetHeaders = jest.fn((additionalHeaders = {}) => ({
  'Content-Type': 'application/json',
  'X-CSRF-Token': 'test-csrf-token',
  ...additionalHeaders,
}));

export const useCSRF = jest.fn(() => ({
  csrfToken: 'test-csrf-token',
  isLoading: false,
  error: null,
  getHeaders: mockGetHeaders,
}));

// Export the mock function for test access
export { mockGetHeaders };
