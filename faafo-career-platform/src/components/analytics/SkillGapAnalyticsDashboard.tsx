'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  AlertTriangle, 
  CheckCircle,
  Users,
  Target,
  BookOpen,
  Zap
} from 'lucide-react';

interface AnalyticsData {
  summary: {
    totalEvents: number;
    eventCounts: Record<string, number>;
    avgProcessingTime: number;
    errorRate: number;
    timeframe: string;
  };
  userAnalytics: {
    totalAssessments: number;
    totalAnalyses: number;
    totalSkillsAssessed: number;
    averageSkillRating: number;
    averageConfidenceLevel: number;
    lastAssessmentDate: string;
    lastAnalysisDate: string;
    totalSkillGaps: number;
    criticalGapsCount: number;
  } | null;
  recentEvents: Array<{
    id: string;
    eventName: string;
    timestamp: string;
    properties: any;
  }>;
  analysisMetrics: Array<{
    id: string;
    analysisId: string;
    gapCount: number;
    criticalGaps: number;
    processingTime: number;
    aiServiceUsed: boolean;
    edgeCaseHandlerUsed: boolean;
    fallbackDataUsed: boolean;
    timestamp: string;
  }>;
  errorAnalytics: Array<{
    id: string;
    errorType: string;
    errorMessage: string;
    context: string;
    recovered: boolean;
    timestamp: string;
  }>;
}

export default function SkillGapAnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeframe, setTimeframe] = useState('7d');
  const [selectedTab, setSelectedTab] = useState('overview');

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeframe]);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/analytics/events?timeframe=${timeframe}`);
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data.data);
      }
    } catch (error) {
      console.error('Failed to fetch analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getEventIcon = (eventName: string) => {
    switch (eventName) {
      case 'skill_assessment_started':
      case 'assessment_submitted':
        return <Users className="h-4 w-4" />;
      case 'gap_analysis_started':
      case 'gap_analysis_completed':
        return <Target className="h-4 w-4" />;
      case 'learning_plan_viewed':
      case 'learning_resource_clicked':
        return <BookOpen className="h-4 w-4" />;
      case 'error_encountered':
      case 'edge_case_triggered':
        return <AlertTriangle className="h-4 w-4" />;
      default:
        return <BarChart3 className="h-4 w-4" />;
    }
  };

  const getEventColor = (eventName: string) => {
    if (eventName.includes('error') || eventName.includes('failed')) {
      return 'text-destructive bg-destructive/10';
    }
    if (eventName.includes('completed') || eventName.includes('submitted')) {
      return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-950/20';
    }
    if (eventName.includes('started') || eventName.includes('viewed')) {
      return 'text-primary bg-secondary';
    }
    return 'text-muted-foreground bg-muted';
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
                  <div className="h-8 w-16 bg-gray-200 rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900">No Analytics Data</h3>
        <p className="text-gray-500">Start using the skill gap analyzer to see analytics.</p>
      </div>
    );
  }

  const { summary, userAnalytics, recentEvents, analysisMetrics, errorAnalytics } = analyticsData;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <Select value={timeframe} onValueChange={setTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1d">Last 24h</SelectItem>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Events</p>
                <p className="text-2xl font-bold">{formatNumber(summary.totalEvents)}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Avg Processing Time</p>
                <p className="text-2xl font-bold">{formatDuration(summary.avgProcessingTime)}</p>
              </div>
              <Clock className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Error Rate</p>
                <p className="text-2xl font-bold">{(summary.errorRate * 100).toFixed(1)}%</p>
              </div>
              {summary.errorRate > 0.05 ? (
                <AlertTriangle className="h-8 w-8 text-red-500" />
              ) : (
                <CheckCircle className="h-8 w-8 text-green-500" />
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-500">Total Assessments</p>
                <p className="text-2xl font-bold">{userAnalytics?.totalAssessments || 0}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="errors">Errors</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {userAnalytics && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Activity Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Total Assessments</p>
                      <p className="font-medium">{userAnalytics.totalAssessments}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Total Analyses</p>
                      <p className="font-medium">{userAnalytics.totalAnalyses}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Skills Assessed</p>
                      <p className="font-medium">{userAnalytics.totalSkillsAssessed}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Critical Gaps</p>
                      <p className="font-medium">{userAnalytics.criticalGapsCount}</p>
                    </div>
                  </div>
                  
                  {userAnalytics.averageSkillRating && (
                    <div className="pt-4 border-t">
                      <div className="flex justify-between text-sm">
                        <span>Avg Skill Rating</span>
                        <span className="font-medium">{userAnalytics.averageSkillRating.toFixed(1)}/10</span>
                      </div>
                      <div className="flex justify-between text-sm mt-1">
                        <span>Avg Confidence</span>
                        <span className="font-medium">{userAnalytics.averageConfidenceLevel?.toFixed(1)}/10</span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Event Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(summary.eventCounts)
                      .sort(([,a], [,b]) => b - a)
                      .slice(0, 8)
                      .map(([eventName, count]) => (
                        <div key={eventName} className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {getEventIcon(eventName)}
                            <span className="text-sm">{eventName.replace(/_/g, ' ')}</span>
                          </div>
                          <Badge variant="outline">{count}</Badge>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Analysis Performance</CardTitle>
                <CardDescription>Processing times and service usage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analysisMetrics.slice(0, 5).map((metric) => (
                    <div key={metric.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="text-sm font-medium">Analysis {metric.analysisId.slice(-8)}</p>
                        <p className="text-xs text-gray-500">{formatDate(metric.timestamp)}</p>
                        <div className="flex gap-2 mt-1">
                          {metric.aiServiceUsed && (
                            <Badge variant="outline" className="text-xs">AI Service</Badge>
                          )}
                          {metric.edgeCaseHandlerUsed && (
                            <Badge variant="outline" className="text-xs">Edge Handler</Badge>
                          )}
                          {metric.fallbackDataUsed && (
                            <Badge variant="outline" className="text-xs text-orange-600">Fallback</Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">{formatDuration(metric.processingTime)}</p>
                        <p className="text-xs text-gray-500">{metric.gapCount} gaps</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Service Usage</CardTitle>
                <CardDescription>AI service and edge case handler usage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">AI Service Usage</span>
                    <span className="font-medium">
                      {analysisMetrics.filter(m => m.aiServiceUsed).length} / {analysisMetrics.length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Edge Case Handler</span>
                    <span className="font-medium">
                      {analysisMetrics.filter(m => m.edgeCaseHandlerUsed).length} / {analysisMetrics.length}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Fallback Data Used</span>
                    <span className="font-medium text-orange-600">
                      {analysisMetrics.filter(m => m.fallbackDataUsed).length} / {analysisMetrics.length}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Errors Tab */}
        <TabsContent value="errors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Error Analytics</CardTitle>
              <CardDescription>Recent errors and recovery patterns</CardDescription>
            </CardHeader>
            <CardContent>
              {errorAnalytics.length === 0 ? (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium">No Errors Recorded</h3>
                  <p className="text-gray-500">Great! No errors in the selected timeframe.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {errorAnalytics.map((error) => (
                    <div key={error.id} className="flex items-start gap-3 p-3 border rounded-lg">
                      <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium text-sm">{error.errorType}</p>
                          <div className="flex items-center gap-2">
                            {error.recovered && (
                              <Badge variant="outline" className="text-green-600">Recovered</Badge>
                            )}
                            <span className="text-xs text-gray-500">{formatDate(error.timestamp)}</span>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{error.errorMessage}</p>
                        <p className="text-xs text-gray-500 mt-1">Context: {error.context}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Events</CardTitle>
              <CardDescription>Latest user interactions and system events</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentEvents.map((event) => (
                  <div key={event.id} className={`flex items-center gap-3 p-3 rounded-lg ${getEventColor(event.eventName)}`}>
                    {getEventIcon(event.eventName)}
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-sm">{event.eventName.replace(/_/g, ' ')}</p>
                        <span className="text-xs opacity-75">{formatDate(event.timestamp)}</span>
                      </div>
                      {event.properties && Object.keys(event.properties).length > 0 && (
                        <p className="text-xs opacity-75 mt-1">
                          {Object.entries(event.properties)
                            .slice(0, 3)
                            .map(([key, value]) => `${key}: ${value}`)
                            .join(', ')}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
